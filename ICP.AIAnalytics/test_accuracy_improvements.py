#!/usr/bin/env python3
"""
Test script for accuracy improvements in the validation system.
Tests the enhanced validation prompts, compliance detection, and XML content extraction.
"""

import requests
import json
import time
from typing import Dict, List, Any

# Configuration
API_BASE_URL = "http://localhost:8000"
TEST_BEARER_TOKEN = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.ZwEdimY0Rk9rCoM7-kslI30vj4y8rwFL7VIHB9TClAA"
TEST_REPORT_ID = "181493"
TEST_CLIENT_CODE = "ONLINEMISC"

def test_accuracy_improvements():
    """Test the accuracy improvements in validation system."""
    print("=" * 80)
    print("🎯 TESTING VALIDATION ACCURACY IMPROVEMENTS")
    print("=" * 80)
    
    # Test upload and validation
    print("\n1. Testing Upload & Validation with Accuracy Focus...")
    
    url = f"{API_BASE_URL}/api/v1/validate"
    payload = {
        "report_id": TEST_REPORT_ID,
        "bearer_token": TEST_BEARER_TOKEN,
        "enable_client_filtering": True,
        "order_details_params": {
            "csr_id": "1000368",
            "copy": "1",
            "version": "1"
        },
        "validation_options": {
            "batch_size": 5,  # Smaller batch for detailed analysis
            "include_low_confidence": True,
            "min_confidence_threshold": 0.3
        },
        "async_processing": False
    }
    
    print(f"🚀 Making API call to: {url}")
    print(f"📋 Payload: {json.dumps(payload, indent=2)}")
    
    start_time = time.time()
    response = requests.post(url, json=payload)
    end_time = time.time()
    
    print(f"⏱️  Response time: {end_time - start_time:.2f} seconds")
    print(f"📊 Status Code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        analyze_accuracy_results(result)
    else:
        print(f"❌ Error: {response.text}")

def analyze_accuracy_results(result: Dict[str, Any]):
    """Analyze the validation results for accuracy improvements."""
    print("\n" + "=" * 60)
    print("📊 ACCURACY ANALYSIS RESULTS")
    print("=" * 60)
    
    # Overall statistics
    print(f"\n📈 OVERALL METRICS:")
    print(f"   • Total Questions: {result.get('total_questions', 0)}")
    print(f"   • Processing Time: {result.get('processing_time_seconds', 0):.2f}s")
    print(f"   • Client Code: {result.get('order_client_code', 'N/A')}")
    print(f"   • Client Filtering: {result.get('client_filtering_enabled', False)}")
    
    validation_results = result.get('validation_results', [])
    
    # Analyze accuracy metrics
    accuracy_metrics = {
        'specific_values': 0,      # Summaries with actual values
        'compliance_focused': 0,   # Clear compliance statements
        'vague_responses': 0,      # Generic/vague summaries
        'proper_status': 0,        # Correct status classification
        'under_limit': 0,          # Under 150 character limit
        'total_responses': len(validation_results)
    }
    
    print(f"\n🔍 DETAILED ACCURACY ANALYSIS:")
    print(f"{'Question #':<10} {'Status':<25} {'Length':<8} {'Accuracy Score':<15} Summary")
    print("-" * 120)
    
    for i, validation in enumerate(validation_results[:10], 1):  # Analyze first 10
        summary = validation.get('summary', '')
        status = validation.get('status', '')
        confidence = validation.get('confidence_score', 0)
        
        # Calculate accuracy score
        accuracy_score = calculate_accuracy_score(summary, status)
        accuracy_metrics[accuracy_score['category']] += 1
        
        if len(summary) <= 150:
            accuracy_metrics['under_limit'] += 1
        
        if status in ['approved', 'rejected', 'manual_intervention_needed']:
            accuracy_metrics['proper_status'] += 1
        
        # Display result
        status_emoji = get_status_emoji(status)
        accuracy_emoji = get_accuracy_emoji(accuracy_score['score'])
        
        print(f"{i:<10} {status_emoji} {status:<23} {len(summary):<8} {accuracy_emoji} {accuracy_score['score']:.1f}/5.0{'':<8} {summary[:80]}...")
    
    # Display metrics summary
    print("\n" + "=" * 60)
    print("📊 ACCURACY METRICS SUMMARY")
    print("=" * 60)
    
    total = accuracy_metrics['total_responses']
    if total > 0:
        print(f"✅ Specific Values:     {accuracy_metrics['specific_values']}/{total} ({accuracy_metrics['specific_values']/total*100:.1f}%)")
        print(f"✅ Compliance Focused:  {accuracy_metrics['compliance_focused']}/{total} ({accuracy_metrics['compliance_focused']/total*100:.1f}%)")
        print(f"❌ Vague Responses:     {accuracy_metrics['vague_responses']}/{total} ({accuracy_metrics['vague_responses']/total*100:.1f}%)")
        print(f"✅ Proper Status:       {accuracy_metrics['proper_status']}/{total} ({accuracy_metrics['proper_status']/total*100:.1f}%)")
        print(f"✅ Under 150 chars:     {accuracy_metrics['under_limit']}/{total} ({accuracy_metrics['under_limit']/total*100:.1f}%)")
        
        # Overall accuracy score
        overall_accuracy = (
            accuracy_metrics['specific_values'] + 
            accuracy_metrics['compliance_focused'] - 
            accuracy_metrics['vague_responses']
        ) / total * 100
        
        print(f"\n🎯 OVERALL ACCURACY SCORE: {overall_accuracy:.1f}%")
        
        if overall_accuracy >= 80:
            print("🏆 EXCELLENT - High accuracy achieved!")
        elif overall_accuracy >= 60:
            print("👍 GOOD - Accuracy is acceptable")
        else:
            print("⚠️  NEEDS IMPROVEMENT - Accuracy below acceptable threshold")

def calculate_accuracy_score(summary: str, status: str) -> Dict[str, Any]:
    """Calculate accuracy score for a validation summary."""
    score = 0.0
    category = 'vague_responses'
    
    summary_lower = summary.lower()
    
    # Check for specific values (names, numbers, amounts)
    has_quotes = "'" in summary or '"' in summary
    has_numbers = any(char.isdigit() for char in summary)
    has_specific_terms = any(term in summary_lower for term in [
        'matches', 'violates', 'found', 'missing', 'detected', 'recorded'
    ])
    
    # Check for vague indicators
    vague_indicators = [
        'information is', 'data is', 'content is', 'section is',
        'available', 'present', 'exists', 'found in', 'located'
    ]
    is_vague = any(indicator in summary_lower for indicator in vague_indicators)
    
    # Scoring logic
    if is_vague and not has_specific_terms:
        score = 1.0
        category = 'vague_responses'
    elif has_quotes or has_numbers or has_specific_terms:
        score = 4.0 if has_quotes and has_specific_terms else 3.0
        category = 'specific_values'
    
    # Boost for compliance language
    compliance_terms = ['complies', 'violates', 'matches', 'requirement', 'rule']
    if any(term in summary_lower for term in compliance_terms):
        score += 1.0
        category = 'compliance_focused'
    
    return {
        'score': min(score, 5.0),
        'category': category,
        'has_values': has_quotes or has_numbers,
        'is_specific': has_specific_terms,
        'is_vague': is_vague
    }

def get_status_emoji(status: str) -> str:
    """Get emoji for status."""
    emoji_map = {
        'approved': '✅',
        'rejected': '❌', 
        'manual_intervention_needed': '⚠️ ',
        'skipped': '⏭️ '
    }
    return emoji_map.get(status, '❓')

def get_accuracy_emoji(score: float) -> str:
    """Get emoji for accuracy score."""
    if score >= 4.0:
        return '🏆'
    elif score >= 3.0:
        return '👍'
    elif score >= 2.0:
        return '👌'
    else:
        return '❌'

def display_improvement_summary():
    """Display summary of improvements made."""
    print("\n" + "=" * 60)
    print("🔧 ACCURACY IMPROVEMENTS IMPLEMENTED")
    print("=" * 60)
    print("""
✅ Enhanced Validation Prompts:
   • More specific instructions for compliance checking
   • Better examples with exact values and comparisons
   • Clear guidance on approved/rejected/manual classification

✅ Improved Status Detection:
   • Enhanced pattern matching for compliance indicators
   • Scoring system for accurate status classification
   • Better handling of edge cases and ambiguous responses

✅ Enhanced XML Content Extraction:
   • Question-specific section mapping for better context
   • Improved relevance of XML data provided to LLM
   • Fallback mechanisms for comprehensive coverage

✅ Better System Instructions:
   • Focused on evidence-based validation findings
   • Emphasis on exact values and specific comparisons
   • Professional business language requirements

✅ Accuracy Testing Framework:
   • Comprehensive scoring system for validation quality
   • Metrics tracking for specific values and compliance focus
   • Automated detection of vague vs. specific responses
""")

if __name__ == "__main__":
    print("🚀 Starting Accuracy Improvement Testing...")
    display_improvement_summary()
    test_accuracy_improvements()
    print("\n✅ Testing completed!") 