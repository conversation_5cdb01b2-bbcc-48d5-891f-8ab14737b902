#!/usr/bin/env python3
"""
Debug script to test license number extraction from XML reports.
This will help identify why the model is using placeholder values instead of actual license numbers.
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, os.getcwd())

from app.services.validation_service import ValidationService
from app.services.file_processor import FileProcessor
from app.models.schemas import Question

async def debug_license_extraction():
    """Debug license number extraction process."""
    print("🔍 Debugging License Number Extraction")
    print("=" * 50)
    
    # Initialize services
    validation_service = ValidationService()
    file_processor = FileProcessor()
    
    # Load a sample report with license numbers
    report_files = list(Path("data/processed").glob("*_report.json"))
    if not report_files:
        print("❌ No processed reports found")
        return
    
    # Use the first available report
    report_file = report_files[0]
    report_id = report_file.stem.replace("_report", "")
    
    print(f"📄 Using report: {report_id}")
    
    # Load report data
    with open(report_file, 'r') as f:
        report_data = json.load(f)
    
    # Create a test license question
    license_question = Question(
        id="test-license",
        question="If the license number provided in the order belongs to a registered branch but is not listed under Related Entities as a Branch, a Special Note must be added and 'Transfer to Principal' should be selected.",
        darwin_reference_sections="Report/LegalStatusSection/RegistrationNumbers, Report/RelatedEntities/Branch, Report/SpecialNotes"
    )
    
    print(f"🔍 Testing question: {license_question.question[:100]}...")
    
    # Test content extraction methods
    print("\n1️⃣ Testing Direct XML Content Extraction:")
    direct_content = validation_service._direct_extract_xml_content(report_data, license_question.question)
    print(f"Direct content length: {len(direct_content)}")
    
    # Look for license numbers in direct content
    if "RegistrationNumberValue" in direct_content:
        print("✅ Found RegistrationNumberValue in direct content")
        # Extract and show license numbers
        lines = direct_content.split('\n')
        for line in lines:
            if "RegistrationNumberValue" in line or "Business Licence" in line:
                print(f"   📋 {line.strip()}")
    else:
        print("❌ No RegistrationNumberValue found in direct content")
    
    print("\n2️⃣ Testing LLM Semantic Extraction:")
    if validation_service.llm:
        try:
            semantic_content = await validation_service._llm_semantic_extract_xml_content(
                report_data,
                license_question.question,
                license_question.darwin_reference_sections
            )
            print(f"Semantic content length: {len(semantic_content)}")

            # Look for license numbers in semantic content
            if "RegistrationNumberValue" in semantic_content:
                print("✅ Found RegistrationNumberValue in semantic content")
                # Extract and show license numbers
                lines = semantic_content.split('\n')
                for line in lines:
                    if "RegistrationNumberValue" in line or "Business Licence" in line:
                        print(f"   📋 {line.strip()}")
            else:
                print("❌ No RegistrationNumberValue found in semantic content")

            # Show a sample of the content being sent to LLM
            print(f"\n📄 Sample of semantic content (first 500 chars):")
            print(f"   {semantic_content[:500]}...")

        except Exception as e:
            print(f"❌ LLM semantic extraction failed: {e}")
    else:
        print("❌ No LLM available for semantic extraction")
    
    print("\n3️⃣ Testing Full Validation Process:")
    try:
        # Test the full validation process
        result = await validation_service._validate_single_question(
            license_question, report_data, 22
        )
        
        print(f"✅ Validation completed")
        print(f"   📊 Status: {result.status}")
        print(f"   📝 Summary: {result.summary}")
        print(f"   🎯 Confidence: {result.confidence_score}")
        print(f"   📍 Relevant sections: {result.relevant_sections}")
        
        # Check if actual license numbers are mentioned
        actual_numbers = ["600", "700", "1000", "900", "800000"]
        found_actual = any(num in result.summary for num in actual_numbers)
        
        if found_actual:
            print("✅ Found actual license numbers in summary!")
        else:
            print("❌ No actual license numbers found in summary")
            if "123456789" in result.summary:
                print("⚠️  Found placeholder number 123456789")
        
    except Exception as e:
        print(f"❌ Full validation failed: {e}")
    
    print("\n4️⃣ Analyzing XML Structure:")
    xml_structure = report_data.get("xml_structure", {})
    registration_numbers = xml_structure.get("Report", {}).get("LegalStatusSection", {}).get("RegistrationNumbers", {})
    
    if registration_numbers:
        print("✅ Found RegistrationNumbers in XML structure:")
        reg_nums = registration_numbers.get("RegistrationNumber", [])
        if isinstance(reg_nums, list):
            for i, reg_num in enumerate(reg_nums):
                value = reg_num.get("RegistrationNumberValue", "N/A")
                name = reg_num.get("ICPRegistrationNumberName", "N/A")
                print(f"   📋 {i+1}. {name}: {value}")
        else:
            value = reg_nums.get("RegistrationNumberValue", "N/A")
            name = reg_nums.get("ICPRegistrationNumberName", "N/A")
            print(f"   📋 {name}: {value}")
    else:
        print("❌ No RegistrationNumbers found in XML structure")

if __name__ == "__main__":
    asyncio.run(debug_license_extraction())
