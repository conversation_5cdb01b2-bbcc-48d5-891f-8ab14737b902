import pandas as pd
import xml.etree.ElementTree as ET
import xmltodict
import json
import uuid
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime
from collections import defaultdict
from app.core.config import settings
from app.models.schemas import Question


class FileProcessor:
    def __init__(self):
        self.upload_path = Path(settings.UPLOAD_PATH)
        self.processed_path = Path(settings.PROCESSED_PATH)

    async def save_uploaded_file(self, file_content: bytes, filename: str, additional_metadata: Optional[Dict[str, Any]] = None) -> str:
        """Save uploaded file and return file ID."""
        file_id = str(uuid.uuid4())
        file_extension = Path(filename).suffix.lower()
        
        # Create timestamped filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        saved_filename = f"{file_id}_{timestamp}{file_extension}"
        
        file_path = self.upload_path / saved_filename
        
        with open(file_path, "wb") as f:
            f.write(file_content)
        
        # Store metadata
        metadata = {
            "file_id": file_id,
            "original_filename": filename,
            "saved_filename": saved_filename,
            "file_size": len(file_content),
            "upload_timestamp": datetime.now().isoformat(),
            "file_type": file_extension
        }
        
        # Add any additional metadata (like report_id, source)
        if additional_metadata:
            metadata.update(additional_metadata)
        
        metadata_path = self.processed_path / f"{file_id}_metadata.json"
        with open(metadata_path, "w") as f:
            json.dump(metadata, f, indent=2)
        
        return file_id

    async def process_excel_questions(self, file_id: str) -> List[Question]:
        """Process Excel file and extract questions with new format support."""
        try:
            # Get file metadata
            metadata_path = self.processed_path / f"{file_id}_metadata.json"
            with open(metadata_path, "r") as f:
                metadata = json.load(f)
            
            # Load Excel file
            file_path = self.upload_path / metadata["saved_filename"]
            
            # Try to detect the correct header row
            df = self._load_excel_with_header_detection(file_path)
            
            questions = []
            
            print(f"Available columns in Excel file after header detection: {list(df.columns)}")
            
            # New format column mappings
            new_format_columns = {
                # Main question column
                "validation details (questions)": "question",
                "validation details": "question", 
                    "question": "question",
                    "questions": "question",
                
                # Client specific column
                "client specific / all report client": "client_specific_type",
                "client specific": "client_specific_type",
                "client": "client_specific_type",
                "client_code": "client_code",
                
                # Darwin reference sections
                "darwin reference section(s)": "darwin_reference_sections",
                "darwin reference sections": "darwin_reference_sections",
                "reference sections": "darwin_reference_sections",
                "reference": "darwin_reference_sections",
                
                # Expected outcome
                "expected outcome": "expected_outcome",
                "outcome": "expected_outcome",
                "expected result": "expected_outcome",
                
                # Editor
                "editor": "editor",
                
                # Legacy optional columns
                "category": "category",
                "priority": "priority",
                "expected_format": "expected_format"
            }
            
            # Create a mapping of actual columns to our standard column names
            column_mapping = {}
            for col in df.columns:
                col_lower = str(col).lower().strip()
                if col_lower in new_format_columns:
                    column_mapping[col] = new_format_columns[col_lower]
                    print(f"Mapped '{col}' -> '{new_format_columns[col_lower]}'")
            
            # Apply column renaming
            df = df.rename(columns=column_mapping)
            
            # Check if we have the main question column
            if "question" not in df.columns:
                # Fallback: find the column with the most descriptive content
                    best_column = None
                    max_avg_length = 0
                    
                    for col in df.columns:
                        if col in column_mapping.values():
                            continue  # Skip already mapped columns
                            
                        # Calculate average length of non-null text in this column
                        text_lengths = []
                        for value in df[col].dropna():
                            if isinstance(value, str) and value.strip():
                                text_lengths.append(len(value.strip()))
                        
                        if text_lengths:
                            avg_length = sum(text_lengths) / len(text_lengths)
                            print(f"Column '{col}' has avg length: {avg_length:.1f}")
                            if avg_length > max_avg_length and avg_length > 20:
                                max_avg_length = avg_length
                                best_column = col
                    
                    if best_column:
                        df = df.rename(columns={best_column: "question"})
                        print(f"Using column '{best_column}' as question column (avg length: {max_avg_length:.1f})")
                    else:
                        raise Exception("Could not find a suitable column for questions. Please ensure the Excel file has proper column headers and a column with validation questions.")
            
            print(f"Final column mapping: {list(df.columns)}")
            
            # Process each row
            for idx, row in df.iterrows():
                if pd.notna(row["question"]) and str(row["question"]).strip():
                    
                    # Extract client specific type and derive client code
                    client_specific_type = str(row.get("client_specific_type", "")).strip() if pd.notna(row.get("client_specific_type")) else None
                    
                    # Derive client_code from client_specific_type
                    client_code = None
                    if client_specific_type:
                        if client_specific_type.lower() == "all":
                            client_code = None  # No client code for "All" questions
                        elif client_specific_type.lower() == "client":
                            client_code = "CLIENT"  # Generic client code
                        else:
                            client_code = client_specific_type  # Use as-is for specific client codes
                    
                    # Also check for direct client_code column (legacy support)
                    if "client_code" in df.columns and pd.notna(row.get("client_code")):
                        client_code = str(row.get("client_code")).strip()
                    
                    question = Question(
                        id=str(uuid.uuid4()),
                        question=str(row["question"]).strip(),
                        category=str(row.get("category", "")).strip() if pd.notna(row.get("category")) else None,
                        priority=str(row.get("priority", "medium")).strip() if pd.notna(row.get("priority")) else "medium",
                        expected_format=str(row.get("expected_format", "")).strip() if pd.notna(row.get("expected_format")) else None,
                        client_code=client_code,
                        # New fields
                        client_specific_type=client_specific_type,
                        darwin_reference_sections=str(row.get("darwin_reference_sections", "")).strip() if pd.notna(row.get("darwin_reference_sections")) else None,
                        expected_outcome=str(row.get("expected_outcome", "")).strip() if pd.notna(row.get("expected_outcome")) else None,
                        editor=str(row.get("editor", "")).strip() if pd.notna(row.get("editor")) else None
                    )
                    questions.append(question)
            
            print(f"Processed {len(questions)} questions from Excel file")
            
            # Save processed questions
            questions_data = [q.model_dump() for q in questions]
            questions_path = self.processed_path / f"{file_id}_questions.json"
            with open(questions_path, "w") as f:
                json.dump(questions_data, f, indent=2)
            
            return questions
            
        except Exception as e:
            raise Exception(f"Error processing Excel file: {str(e)}")

    def _load_excel_with_header_detection(self, file_path: str) -> pd.DataFrame:
        """Load Excel file with intelligent header row detection."""
        try:
            # First, try to read without specifying header to see the raw content
            df_raw = pd.read_excel(file_path, header=None)
            
            print(f"Raw Excel file shape: {df_raw.shape}")
            print(f"First few rows:")
            for i in range(min(5, len(df_raw))):
                print(f"Row {i}: {list(df_raw.iloc[i])}")
            
            # Look for the header row by checking for expected column patterns
            header_row = None
            expected_patterns = [
                "validation details",
                "client specific",
                "darwin reference",
                "expected outcome",
                "editor",
                "question",
                "category"
            ]
            
            for row_idx in range(min(10, len(df_raw))):  # Check first 10 rows
                row_values = [str(val).lower().strip() for val in df_raw.iloc[row_idx] if pd.notna(val)]
                
                # Check if this row contains header-like patterns
                pattern_matches = 0
                for pattern in expected_patterns:
                    for val in row_values:
                        if pattern in val:
                            pattern_matches += 1
                            break
                
                print(f"Row {row_idx} has {pattern_matches} pattern matches")
                
                # If we find multiple pattern matches, this is likely the header row
                if pattern_matches >= 2:
                    header_row = row_idx
                    print(f"Detected header row at index: {header_row}")
                    break
            
            # If no clear header row found, check for rows with mostly text (not numbers/dates)
            if header_row is None:
                for row_idx in range(min(5, len(df_raw))):
                    row_values = [val for val in df_raw.iloc[row_idx] if pd.notna(val)]
                    text_count = sum(1 for val in row_values if isinstance(val, str) and len(str(val).strip()) > 3)
                    
                    if text_count >= 3:  # Row has at least 3 text columns
                        header_row = row_idx
                        print(f"Using row {header_row} as header based on text content")
                        break
            
            # Load with detected header row
            if header_row is not None:
                df = pd.read_excel(file_path, header=header_row)
                # Skip any rows before the header row that might be empty or contain metadata
                df = df.dropna(how='all')  # Remove completely empty rows
            else:
                # Fallback: use default header detection
                print("No clear header row detected, using default parsing")
                df = pd.read_excel(file_path)
            
            return df
            
        except Exception as e:
            print(f"Header detection failed: {e}")
            # Final fallback: standard Excel reading
            return pd.read_excel(file_path)

    async def process_xml_report(self, file_id: str) -> Dict[str, Any]:
        """Process XML report and convert to structured data."""
        try:
            # Get file metadata
            metadata_path = self.processed_path / f"{file_id}_metadata.json"
            with open(metadata_path, "r") as f:
                metadata = json.load(f)
            
            # Load XML file
            file_path = self.upload_path / metadata["saved_filename"]
            
            with open(file_path, "r", encoding="utf-8") as f:
                xml_content = f.read()
            
            # Parse XML to dictionary
            xml_dict = xmltodict.parse(xml_content)
            
            # Also parse with ElementTree for more detailed processing
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            # Extract text content for embedding
            text_content = self._extract_text_from_xml(root)
            
            # Create structured report data
            report_data = {
                "file_id": file_id,
                "xml_structure": xml_dict,
                "text_content": text_content,
                "root_element": root.tag,
                "namespace": root.attrib,
                "processing_timestamp": datetime.now().isoformat()
            }
            
            # Save processed report
            report_path = self.processed_path / f"{file_id}_report.json"
            with open(report_path, "w") as f:
                json.dump(report_data, f, indent=2, default=str)
            
            return report_data
            
        except Exception as e:
            raise Exception(f"Error processing XML file: {str(e)}")

    def _extract_text_from_xml(self, element: ET.Element, path: str = "") -> List[Dict[str, str]]:
        """Extract text content from XML elements with their paths."""
        text_content = []
        
        current_path = f"{path}/{element.tag}" if path else element.tag
        
        # Add element text if it exists and is not just whitespace
        if element.text and element.text.strip():
            text_content.append({
                "path": current_path,
                "tag": element.tag,
                "text": element.text.strip(),
                "attributes": element.attrib
            })
        
        # Process child elements
        for child in element:
            text_content.extend(self._extract_text_from_xml(child, current_path))
        
        # Add tail text if it exists
        if element.tail and element.tail.strip():
            text_content.append({
                "path": f"{current_path}_tail",
                "tag": f"{element.tag}_tail",
                "text": element.tail.strip(),
                "attributes": {}
            })
        
        return text_content

    async def get_file_metadata(self, file_id: str) -> Optional[Dict[str, Any]]:
        """Get file metadata by file ID."""
        try:
            metadata_path = self.processed_path / f"{file_id}_metadata.json"
            if metadata_path.exists():
                with open(metadata_path, "r") as f:
                    return json.load(f)
            return None
        except Exception:
            return None

    async def get_processed_questions(self, file_id: str) -> Optional[List[Question]]:
        """Get processed questions by file ID."""
        try:
            questions_path = self.processed_path / f"{file_id}_questions.json"
            if questions_path.exists():
                with open(questions_path, "r") as f:
                    questions_data = json.load(f)
                return [Question(**q) for q in questions_data]
            return None
        except Exception:
            return None

    async def get_processed_report(self, file_id: str) -> Optional[Dict[str, Any]]:
        """Get processed report by file ID."""
        try:
            report_path = self.processed_path / f"{file_id}_report.json"
            if report_path.exists():
                with open(report_path, "r") as f:
                    return json.load(f)
            return None
        except Exception:
            return None

    async def resolve_report_id_to_file_id(self, report_id: str) -> Optional[str]:
        """Resolve report_id to file_id by searching metadata files."""
        try:
            # Search through all metadata files to find one with matching report_id
            for metadata_file in self.processed_path.glob("*_metadata.json"):
                try:
                    with open(metadata_file, "r") as f:
                        metadata = json.load(f)
                    
                    # Check if this metadata has a matching report_id
                    if metadata.get("report_id") == report_id:
                        return metadata.get("file_id")
                        
                    # Also check if the report_id matches the file_id (for backward compatibility)
                    if metadata.get("file_id") == report_id:
                        return metadata.get("file_id")
                        
                except Exception:
                    continue  # Skip corrupted metadata files
            
            return None
        except Exception:
            return None

    async def get_processed_report_by_report_id(self, report_id: str) -> Optional[Dict[str, Any]]:
        """Get processed report by report_id (resolves to file_id internally)."""
        try:
            # First try to resolve report_id to file_id
            file_id = await self.resolve_report_id_to_file_id(report_id)
            
            if file_id:
                # Use the resolved file_id to get the processed report
                report_data = await self.get_processed_report(file_id)
                if report_data:
                    # Ensure the report_data includes the file_id for vector store operations
                    report_data["file_id"] = file_id
                    report_data["report_id"] = report_id
                return report_data
            else:
                # If no resolution found, try using report_id directly as file_id (backward compatibility)
                report_data = await self.get_processed_report(report_id)
                if report_data:
                    # Ensure the report_data includes the file_id for vector store operations
                    report_data["file_id"] = report_id
                    report_data["report_id"] = report_id
                return report_data
                
        except Exception:
            return None

    async def load_permanent_questions(self, force_reload: bool = False) -> List[Question]:
        """Load questions from permanent question bank."""
        try:
            if not settings.USE_PERMANENT_QUESTION_BANK:
                raise Exception("Permanent question bank is not enabled")
            
            # Force use the correct file path
            correct_path = "./data/Copy of Prompts Checking AI (2).xlsx"
            permanent_file_path = Path(correct_path)
            absolute_path = permanent_file_path.resolve()
            
            print(f" PERMANENT QUESTION BANK DEBUG:")
            print(f"   - Forced path: {correct_path}")
            print(f"   - Configured path: {settings.PERMANENT_QUESTION_BANK_PATH}")
            print(f"   - Absolute path: {absolute_path}")
            print(f"   - File exists: {permanent_file_path.exists()}")
            print(f"   - Force reload: {force_reload}")
            
            if not permanent_file_path.exists():
                print(f" ERROR: Permanent question bank file not found!")
                print(f"   - Checked path: {permanent_file_path}")
                print(f"   - Working directory: {Path.cwd()}")
                raise Exception(f"Permanent question bank file not found: {permanent_file_path}")
            
            # Get file modification time for debugging
            import os
            mod_time = os.path.getmtime(permanent_file_path)
            mod_datetime = datetime.fromtimestamp(mod_time)
            file_size = os.path.getsize(permanent_file_path)
            
            print(f"   - File modified: {mod_datetime}")
            print(f"   - File size: {file_size} bytes")
            print(f"Loading permanent questions from: {absolute_path}")
            
            # Use the enhanced Excel processing with header detection
            df = self._load_excel_with_header_detection(str(permanent_file_path))
            
            questions = []
            
            print(f"Excel file loaded successfully:")
            print(f"   - Shape: {df.shape}")
            print(f"   - Available columns: {list(df.columns)}")
            
            # Use the same column mapping logic as regular Excel processing
            new_format_columns = {
                # Main question column
                "validation details (questions)": "question",
                "validation details": "question", 
                "question": "question",
                "questions": "question",
                
                # Client specific column
                "client specific / all report client": "client_specific_type",
                "client specific": "client_specific_type",
                "client": "client_specific_type",
                "client_code": "client_code",
                
                # Darwin reference sections
                "darwin reference section(s)": "darwin_reference_sections",
                "darwin reference sections": "darwin_reference_sections",
                "reference sections": "darwin_reference_sections",
                "reference": "darwin_reference_sections",
                
                # Expected outcome
                "expected outcome": "expected_outcome",
                "outcome": "expected_outcome",
                "expected result": "expected_outcome",
                
                # Editor
                "editor": "editor",
                
                # Legacy optional columns
                "category": "category",
                "priority": "priority",
                "expected_format": "expected_format"
            }
            
            # Create a mapping of actual columns to our standard column names
            column_mapping = {}
            for col in df.columns:
                col_lower = str(col).lower().strip()
                if col_lower in new_format_columns:
                    column_mapping[col] = new_format_columns[col_lower]
                    print(f"Mapped '{col}' -> '{new_format_columns[col_lower]}'")
            
            # Apply column renaming
            df = df.rename(columns=column_mapping)
            
            # Check if we have the main question column
            if "question" not in df.columns:
                # Fallback: find the column with the most descriptive content
                best_column = None
                max_avg_length = 0
                
                for col in df.columns:
                    if col in column_mapping.values():
                        continue  # Skip already mapped columns
                        
                    # Calculate average length of non-null text in this column
                    text_lengths = []
                    for value in df[col].dropna():
                        if isinstance(value, str) and value.strip():
                            text_lengths.append(len(value.strip()))
                    
                    if text_lengths:
                        avg_length = sum(text_lengths) / len(text_lengths)
                        print(f" Column '{col}' has avg length: {avg_length:.1f}")
                        if avg_length > max_avg_length and avg_length > 20:
                            max_avg_length = avg_length
                            best_column = col
                
                if best_column:
                    df = df.rename(columns={best_column: "question"})
                    print(f" Using column '{best_column}' as question column (avg length: {max_avg_length:.1f})")
                else:
                    raise Exception("Could not find a suitable column for questions in permanent question bank.")
            
            print(f" Final column mapping for permanent questions: {list(df.columns)}")
            
            # Process each row
            for idx, row in df.iterrows():
                if pd.notna(row["question"]) and str(row["question"]).strip():
                    
                    # Extract client specific type and derive client code
                    client_specific_type = str(row.get("client_specific_type", "")).strip() if pd.notna(row.get("client_specific_type")) else None
                    
                    # Derive client_code from client_specific_type
                    client_code = None
                    if client_specific_type:
                        if client_specific_type.lower() == "all":
                            client_code = None  # No client code for "All" questions
                        elif client_specific_type.lower() == "client":
                            client_code = "CLIENT"  # Generic client code
                        else:
                            client_code = client_specific_type  # Use as-is for specific client codes
                    
                    # Also check for direct client_code column (legacy support)
                    if "client_code" in df.columns and pd.notna(row.get("client_code")):
                        client_code = str(row.get("client_code")).strip()
                    
                    question = Question(
                        id=str(uuid.uuid4()),
                        question=str(row["question"]).strip(),
                        category=str(row.get("category", "")).strip() if pd.notna(row.get("category")) else None,
                        priority=str(row.get("priority", "medium")).strip() if pd.notna(row.get("priority")) else "medium",
                        expected_format=str(row.get("expected_format", "")).strip() if pd.notna(row.get("expected_format")) else None,
                        client_code=client_code,
                        # New fields
                        client_specific_type=client_specific_type,
                        darwin_reference_sections=str(row.get("darwin_reference_sections", "")).strip() if pd.notna(row.get("darwin_reference_sections")) else None,
                        expected_outcome=str(row.get("expected_outcome", "")).strip() if pd.notna(row.get("expected_outcome")) else None,
                        editor=str(row.get("editor", "")).strip() if pd.notna(row.get("editor")) else None
                    )
                    questions.append(question)
            
            print(f" SUCCESS: Loaded {len(questions)} questions from permanent question bank")
            
            # Print first few questions for verification
            if questions:
                print(f" First 3 questions preview:")
                for i, q in enumerate(questions[:3]):
                    print(f"   {i+1}. {q.question[:100]}{'...' if len(q.question) > 100 else ''}")
                    if q.client_specific_type:
                        print(f"      Client: {q.client_specific_type}")
                    if q.darwin_reference_sections:
                        print(f"      Darwin Ref: {q.darwin_reference_sections[:50]}{'...' if len(q.darwin_reference_sections) > 50 else ''}")
            
            return questions
            
        except Exception as e:
            print(f" ERROR loading permanent questions: {str(e)}")
            raise Exception(f"Error loading permanent questions: {str(e)}")

    async def update_permanent_question_bank(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Update the permanent question bank with a new Excel file."""
        try:
            # Save the new file to the permanent location
            permanent_file_path = Path(settings.PERMANENT_QUESTION_BANK_PATH)
            
            # Create directory if it doesn't exist
            permanent_file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Backup existing file if it exists
            if permanent_file_path.exists():
                backup_path = permanent_file_path.with_suffix(f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx")
                permanent_file_path.rename(backup_path)
                print(f"Backed up existing permanent question bank to: {backup_path}")
            
            # Save new file
            with open(permanent_file_path, "wb") as f:
                f.write(file_content)
            
            print(f"Updated permanent question bank: {permanent_file_path}")
            
            # Test loading the new questions
            questions = await self.load_permanent_questions(force_reload=True)
            
            return {
                "success": True,
                "message": f"Permanent question bank updated successfully",
                "file_path": str(permanent_file_path),
                "total_questions": len(questions),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            raise Exception(f"Error updating permanent question bank: {str(e)}")

def extract_text_and_ids(xml_root):
    """
    Recursively extract text and generate unique IDs for each node.
    """
    texts = []
    # Counter for each parent_path/tag combination
    path_tag_counts = defaultdict(int)

    def recurse(node, parent_path):
        tag = node.tag
        current_path = f"{parent_path}/{tag}" if parent_path else tag

        # Increment the count for this tag under this parent path
        path_tag_counts[current_path] += 1
        unique_id = f"{current_path}_{path_tag_counts[current_path]}"

        # If the node has text, add it with the unique ID
        if node.text and node.text.strip():
            texts.append({
                "id": unique_id,
                "text": node.text.strip()
            })

        # Recurse for children
        for child in node:
            recurse(child, current_path)

    recurse(xml_root, "")
    return texts