#!/usr/bin/env python3
"""
Test license number extraction for specific report ID 1969882
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, os.getcwd())

from app.services.validation_service import ValidationService
from app.services.file_processor import FileProcessor
from app.models.schemas import Question

async def test_report_1969882():
    """Test license number extraction for report ID 1969882."""
    print("🔍 Testing Report ID: 1969882")
    print("=" * 50)
    
    # Initialize services
    validation_service = ValidationService()
    file_processor = FileProcessor()
    
    try:
        # Load the specific report by report_id
        print(f"📄 Loading report with ID: 1969882")
        report_data = await file_processor.get_processed_report_by_report_id("1969882")
        
        if not report_data:
            print("❌ Report not found")
            return
        
        print(f"✅ Report loaded successfully")
        print(f"   File ID: {report_data.get('file_id', 'N/A')}")
        
        # Create the license question from your example
        license_question = Question(
            id="test-license-22",
            question="If the license number provided in the order belongs to a registered branch but is not listed under Related Entities as a Branch, a Special Note must be added and 'Transfer to Principal' should be selected.",
            darwin_reference_sections="Report/LegalStatusSection/RegistrationNumbers, Report/RelatedEntities/Branch, Report/SpecialNotes"
        )
        
        print(f"\n🔍 Testing license question...")
        
        # 1. Check what license numbers exist in this report
        print("\n1️⃣ Analyzing License Numbers in Report:")
        xml_structure = report_data.get("xml_structure", {})
        registration_numbers = xml_structure.get("Report", {}).get("LegalStatusSection", {}).get("RegistrationNumbers", {})
        
        if registration_numbers:
            print("✅ Found RegistrationNumbers in XML structure:")
            reg_nums = registration_numbers.get("RegistrationNumber", [])
            if isinstance(reg_nums, list):
                for i, reg_num in enumerate(reg_nums):
                    value = reg_num.get("RegistrationNumberValue", "N/A")
                    name = reg_num.get("ICPRegistrationNumberName", "N/A")
                    print(f"   📋 {i+1}. {name}: {value}")
            else:
                value = reg_nums.get("RegistrationNumberValue", "N/A")
                name = reg_nums.get("ICPRegistrationNumberName", "N/A")
                print(f"   📋 {name}: {value}")
        else:
            print("❌ No RegistrationNumbers found in XML structure")
        
        # 2. Test content extraction
        print("\n2️⃣ Testing Content Extraction:")
        extracted_content = await validation_service._extract_xml_content_for_question_with_darwin(
            report_data, license_question
        )
        
        print(f"Extracted content length: {len(extracted_content)}")
        
        # Look for license numbers in extracted content
        actual_numbers = []
        if "RegistrationNumberValue" in extracted_content:
            print("✅ Found RegistrationNumberValue in extracted content")
            lines = extracted_content.split('\n')
            for line in lines:
                if "RegistrationNumberValue" in line:
                    # Extract the number from the line
                    if ">" in line and "</" in line:
                        start = line.find(">") + 1
                        end = line.find("</")
                        if start > 0 and end > start:
                            number = line[start:end].strip()
                            if number and number != "RegistrationNumberValue":
                                actual_numbers.append(number)
                                print(f"   📋 Found license number: {number}")
        else:
            print("❌ No RegistrationNumberValue found in extracted content")
        
        # 3. Test full validation
        print("\n3️⃣ Testing Full Validation:")
        result = await validation_service._validate_single_question(
            license_question, report_data, 22
        )
        
        print(f"✅ Validation completed")
        print(f"   📊 Status: {result.status}")
        print(f"   📝 Summary: {result.summary}")
        print(f"   🎯 Confidence: {result.confidence_score}")
        print(f"   📍 Relevant sections: {result.relevant_sections}")
        
        # 4. Check if actual license numbers are in the summary
        print("\n4️⃣ License Number Analysis:")
        if actual_numbers:
            print(f"   📋 Actual license numbers found: {actual_numbers}")
            found_in_summary = any(num in result.summary for num in actual_numbers)
            
            if found_in_summary:
                print("✅ Found actual license numbers in summary!")
                for num in actual_numbers:
                    if num in result.summary:
                        print(f"   ✓ License number {num} is mentioned in summary")
            else:
                print("❌ No actual license numbers found in summary")
                print(f"   Expected to find one of: {actual_numbers}")
                print(f"   But summary says: {result.summary}")
                
                # Check for placeholder numbers
                if "123456789" in result.summary:
                    print("⚠️  Found placeholder number 123456789 instead of actual number")
        else:
            print("❌ No license numbers found in the report")
        
        # 5. Show the exact content being sent to LLM
        print("\n5️⃣ Content Sent to LLM (first 1000 chars):")
        print(f"   {extracted_content[:1000]}...")
        
    except Exception as e:
        print(f"❌ Error testing report: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_report_1969882())
